<?php

namespace App\Services\Center;

use App\Models\Main\Center;
use App\Repositories\Admin\CenterRepository;
use Illuminate\Validation\ValidationException;
use App\Repositories\Admin\CenterStaffRepository;


class CenterService
{

    public function __construct(
        protected CenterRepository $centerRepository,
        protected CenterStaffRepository $centerStaffRepository
    ) {}

    /**
     * 建立新中心
     *
     * @param array $data 中心資料
     * @return Center
     * @throws ValidationException
     */
    public function createCenter(array $data): Center
    {
        $center = $this->centerRepository->create($data);
        return $center->load(['level', 'activeStaff.role', 'activeStaff.account']);
    }

    /**
     * 更新中心資料
     *
     * @param int $id 中心ID
     * @param array $data 更新資料
     * @return Center
     */
    public function updateCenter(int $id, array $data): Center
    {
        $center = $this->centerRepository->updateCenterByID($id, $data);
        return $center->load(['level', 'activeStaff.role', 'activeStaff.account']);
    }

    /**
     * 刪除中心
     *
     * @param int $id 中心ID
     * @return bool
     * @throws \RuntimeException
     */
    public function deleteCenter(int $id): bool
    {
        $center = $this->centerRepository->findOrFail($id);

        // 檢查是否可以刪除
        $activeStaffCount = $this->centerStaffRepository->getActiveStaffCountByCenterId($id);

        if ($activeStaffCount > 0) {
            throw new \RuntimeException("無法刪除：中心「{$center->name}」仍有 {$activeStaffCount} 位現役人員");
        }

        return $center->delete();
    }

    /**
     * 批量刪除中心
     *
     * @param array $ids
     * @return array
     */
    public function batchDeleteCenters(array $ids): array
    {
        $deleted = [];
        $failed = [];

        foreach ($ids as $id) {
            try {
                $this->deleteCenter($id);
                $deleted[] = $id;
            } catch (\RuntimeException $e) {
                $failed[] = ['id' => $id, 'reason' => $e->getMessage()];
            }
        }

        $message = "成功刪除 " . count($deleted) . " 個中心";
        if (count($failed) > 0) {
            $message .= "，" . count($failed) . " 個中心無法刪除";
        }

        return [
            'message' => $message,
            'data' => ['deleted' => $deleted, 'failed' => $failed]
        ];
    }
}
