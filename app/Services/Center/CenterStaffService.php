<?php

namespace App\Services\Center;

use App\Models\Main\CenterStaff;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use App\Repositories\Admin\AccountRepository;
use App\Repositories\Admin\CenterRepository;
use App\Repositories\Admin\CenterRoleRepository;
use App\Repositories\Admin\CenterStaffRepository;

/**
 * CenterStaffService
 */
class CenterStaffService
{
    public function __construct(
        protected AccountRepository $accountRepository,
        protected CenterRepository $centerRepository,
        protected CenterRoleRepository $centerRoleRepository,
        protected CenterStaffRepository $centerStaffRepository
    ) {}

    /**
     * 指派角色
     *
     * @param int $centerId
     * @param int $accountId
     * @param string $roleCode
     * @param Carbon|null $startAt
     * @param bool $endExistingSingleton 若為 singleton 且已有現役：true=結束舊任期再插入, false=丟出例外
     * @return CenterStaff
     *
     * @throws ModelNotFoundException| \RuntimeException
     */
    public function assignRole(
        int $centerId,
        int $accountId,
        string $roleCode,
        ?Carbon $startAt = null,
        bool $endExistingSingleton = true
    ): CenterStaff {
        $startAt = $startAt ?? Carbon::now();

        return DB::connection('main_db')->transaction(function () use (
            $centerId,
            $accountId,
            $roleCode,
            $startAt,
            $endExistingSingleton
        ) {
            // 使用 Repository 查找角色
            $role = $this->centerRoleRepository->findAssignableByCode($roleCode);
            if (!$role) {
                throw new ModelNotFoundException("CenterRole not assignable: {$roleCode}");
            }

            // 驗證中心存在
            $this->centerRepository->findOrFail($centerId);

            // 若 singleton：檢查現役記錄
            if ($role->isSingleton()) {
                $hasActiveSingleton = $this->centerStaffRepository->hasActiveSingleton($centerId, $role->id);

                if ($hasActiveSingleton) {
                    if ($endExistingSingleton) {
                        // 結束現有的單例角色
                        $existingActive = $this->centerStaffRepository->getActiveStaffByRole($centerId, $role->id)->first();
                        if ($existingActive) {
                            $this->centerStaffRepository->update($existingActive, ['end_at' => Carbon::now()]);
                        }
                    } else {
                        throw new \RuntimeException("Singleton role '{$roleCode}' already active in center {$centerId}");
                    }
                }
            }

            // 建立新任期
            return $this->centerStaffRepository->create([
                'center_id'  => $centerId,
                'account_id' => $accountId,
                'role_id'    => $role->id,
                'start_at'   => $startAt,
                'note'       => null,
            ]);
        });
    }

    /**
     * 結束指定任期（若尚未結束）
     *
     * @param int $centerStaffId
     * @param Carbon|null $endAt
     * @return CenterStaff
     * @throws ModelNotFoundException
     */
    public function endRole(int $centerStaffId, ?Carbon $endAt = null): CenterStaff
    {
        $staff = $this->centerStaffRepository->findOrFail($centerStaffId);

        if ($staff->end_at === null) {
            $this->centerStaffRepository->update($staff, [
                'end_at' => $endAt ?? Carbon::now()
            ]);
        }

        return $staff;
    }

    /**
     * 根據 center + account + roleCode 結束現役任期（若存在）
     *
     * @return CenterStaff|null 已結束的任期；若無現役返回 null
     */
    public function endActiveRole(int $centerId, int $accountId, string $roleCode, ?Carbon $endAt = null): ?CenterStaff
    {
        $role = $this->centerRoleRepository->findByCode($roleCode);
        if (!$role) {
            return null;
        }

        $staff = $this->centerStaffRepository->findActiveStaff($centerId, $accountId, $role->id);

        if ($staff) {
            $this->centerStaffRepository->update($staff, [
                'end_at' => $endAt ?? Carbon::now()
            ]);
        }

        return $staff;
    }

    /**
     * 續任：結束目前現役後建立新任期
     *
     * @param int $centerStaffId
     * @param Carbon|null $newStart
     * @return CenterStaff 新任期
     */
    public function renewRole(int $centerStaffId, ?Carbon $newStart = null): CenterStaff
    {
        $staff = $this->centerStaffRepository->findOrFail($centerStaffId);

        return DB::connection('main_db')->transaction(function () use ($staff, $newStart) {
            $ended = $this->endRole($staff->id);
            return $this->centerStaffRepository->create([
                'center_id'  => $ended->center_id,
                'account_id' => $ended->account_id,
                'role_id'    => $ended->role_id,
                'start_at'   => $newStart ?? Carbon::now(),
                'note'       => 'renew from ' . $ended->id,
            ]);
        });
    }

    /**
     * 取得某中心某角色現役集合
     *
     * @param int $centerId
     * @param string $roleCode
     * @return \Illuminate\Support\Collection<CenterStaff>
     */
    public function getActiveStaff(int $centerId, string $roleCode)
    {
        $role = $this->centerRoleRepository->findByCode($roleCode);
        if (!$role) {
            return collect();
        }
        return $this->centerStaffRepository->getActiveStaffByRole($centerId, $role->id);
    }

    /**
     * 檢查 singleton 角色是否已存在現役
     */
    public function hasActiveSingleton(int $centerId, string $roleCode): bool
    {
        $role = $this->centerRoleRepository->findSingletonByCode($roleCode);
        if (!$role) {
            return false;
        }

        return $this->centerStaffRepository->hasActiveSingleton($centerId, $role->id);
    }

    /**
     * 根據會員編號指派角色
     */
    public function assignRoleByAccountNumber(
        int $centerId,
        string $accountNumber,
        string $roleCode,
        ?string $startAt = null
    ): array {
        $account = $this->accountRepository->findByNumber($accountNumber);
        if (!$account) {
            throw new \RuntimeException('找不到會員編號：' . $accountNumber);
        }

        $startAtCarbon = $startAt ? Carbon::parse($startAt) : null;

        $staff = $this->assignRole($centerId, $account->id, $roleCode, $startAtCarbon);

        return [
            'id' => $staff->id,
            'center_id' => $staff->center_id,
            'account_id' => $staff->account_id,
            'role_code' => $roleCode,
            'start_at' => $staff->start_at,
            'account' => [
                'id' => $account->id,
                'number' => $account->number,
                'name' => $account->name
            ]
        ];
    }

    /**
     * 移除角色
     */
    public function removeRole(int $centerId, int $accountId, string $roleCode): array
    {
        $staff = $this->endActiveRole($centerId, $accountId, $roleCode);

        if (!$staff) {
            throw new \RuntimeException('找不到對應的角色記錄');
        }

        $account = $this->accountRepository->findById($accountId);

        return [
            'removed_staff_id' => $staff->id,
            'center_id' => $staff->center_id,
            'account_id' => $staff->account_id,
            'role_code' => $roleCode,
            'end_at' => $staff->end_at,
            'account' => [
                'id' => $account->id ?? $accountId,
                'number' => $account->number ?? '',
                'name' => $account->name ?? ''
            ]
        ];
    }

    /**
     * 批量指派角色
     */
    public function batchAssignRole(
        int $centerId,
        array $accountIds,
        string $roleCode,
        ?string $startAt = null
    ): array {
        $role = $this->centerRoleRepository->findByCode($roleCode);
        if (!$role) {
            throw new \RuntimeException('角色不存在');
        }

        if ($role->is_singleton && count($accountIds) > 1) {
            throw new \RuntimeException('單例角色不支援批量指派，請逐一指派');
        }

        $successful = [];
        $failed = [];
        $startAtCarbon = $startAt ? Carbon::parse($startAt) : null;

        foreach ($accountIds as $accountId) {
            try {
                // 檢查該會員是否已經在此中心擔任此角色
                $existingActiveRole = $this->getActiveStaff($centerId, $roleCode)
                    ->where('account_id', $accountId)
                    ->first();

                if ($existingActiveRole) {
                    $failed[] = [
                        'account_id' => $accountId,
                        'reason' => '該會員已在此中心擔任此角色'
                    ];
                    continue;
                }

                $result = $this->assignRole($centerId, $accountId, $roleCode, $startAtCarbon, false);

                $successful[] = [
                    'account_id' => $accountId,
                    'staff_id' => $result->id
                ];
            } catch (\Exception $e) {
                $failed[] = [
                    'account_id' => $accountId,
                    'reason' => $e->getMessage()
                ];
            }
        }

        return [
            'successful' => $successful,
            'failed' => $failed,
            'successful_count' => count($successful),
            'failed_count' => count($failed)
        ];
    }

    /**
     * 批量移除角色
     */
    public function batchRemoveRole(
        int $centerId,
        array $accountIds,
        string $roleCode,
        ?string $endAt = null
    ): array {
        $successful = [];
        $failed = [];
        $endAtCarbon = $endAt ? Carbon::parse($endAt) : null;

        foreach ($accountIds as $accountId) {
            try {
                $endedStaff = $this->endActiveRole($centerId, $accountId, $roleCode, $endAtCarbon);

                if ($endedStaff) {
                    $successful[] = [
                        'account_id' => $accountId,
                        'staff_id' => $endedStaff->id
                    ];
                } else {
                    $failed[] = [
                        'account_id' => $accountId,
                        'reason' => '該會員目前未在此中心擔任此角色'
                    ];
                }
            } catch (\Exception $e) {
                $failed[] = [
                    'account_id' => $accountId,
                    'reason' => $e->getMessage()
                ];
            }
        }

        return [
            'successful' => $successful,
            'failed' => $failed,
            'successful_count' => count($successful),
            'failed_count' => count($failed)
        ];
    }

    /**
     * 驗證角色指派
     */
    public function validateRoleAssignment(int $centerId, int $accountId, string $roleCode): array
    {
        $role = $this->centerRoleRepository->findByCode($roleCode);
        if (!$role) {
            return [
                'valid' => false,
                'warnings' => [],
                'conflicts' => ['角色不存在']
            ];
        }

        $validationResult = [
            'valid' => true,
            'warnings' => [],
            'conflicts' => []
        ];

        // 檢查該會員是否已經在此中心擔任此角色
        $existingActiveRole = $this->getActiveStaff($centerId, $roleCode)
            ->where('account_id', $accountId)
            ->first();

        if ($existingActiveRole) {
            $validationResult['valid'] = false;
            $validationResult['conflicts'][] = '該會員已在此中心擔任此角色';
        }

        // 檢查單例角色衝突
        if ($role->is_singleton) {
            $hasActiveSingleton = $this->hasActiveSingleton($centerId, $roleCode);

            if ($hasActiveSingleton) {
                $currentSingleton = $this->getActiveStaff($centerId, $roleCode)->first();
                if ($currentSingleton && $currentSingleton->account_id !== $accountId) {
                    $currentAccount = $this->accountRepository->findById($currentSingleton->account_id);

                    $validationResult['warnings'][] = [
                        'type' => 'singleton_conflict',
                        'message' => '此角色目前由其他會員擔任，指派後將自動結束其任期',
                        'current_holder' => [
                            'account_id' => $currentSingleton->account_id,
                            'number' => $currentAccount->number ?? '',
                            'name' => $currentAccount->name ?? ''
                        ]
                    ];
                }
            }
        }

        return $validationResult;
    }

    /**
     * 取得中心角色狀態
     */
    public function getRoleStatus(int $centerId, array $roleCodes = []): array
    {
        // 如果沒有指定角色，取得所有角色
        if (empty($roleCodes)) {
            $roleCodes = ['founder', 'director', 'executive_director', 'lecturer', 'market', 'sales'];
        }

        $roleStatus = [];

        foreach ($roleCodes as $roleCode) {
            $role = $this->centerRoleRepository->findByCode($roleCode);
            if (!$role) {
                continue;
            }

            // 取得現役人員
            $activeStaff = $this->getActiveStaff($centerId, $roleCode);

            $staffList = $activeStaff->map(function ($staff) {
                $account = $this->accountRepository->findById($staff->account_id);

                return [
                    'staff_id' => $staff->id,
                    'account_id' => $staff->account_id,
                    'account_number' => $account->number ?? '',
                    'account_name' => $account->name ?? '',
                    'start_at' => $staff->start_at,
                    'note' => $staff->note
                ];
            });

            $roleStatus[$roleCode] = [
                'role_name' => $role->name,
                'is_singleton' => (bool)$role->is_singleton,
                'active_count' => $activeStaff->count(),
                'staff_list' => $staffList,
                'can_assign_more' => !$role->is_singleton || $activeStaff->count() === 0
            ];
        }

        return [
            'center_id' => $centerId,
            'roles' => $roleStatus,
            'updated_at' => now()->toISOString()
        ];
    }
}
