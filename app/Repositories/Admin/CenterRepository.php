<?php

namespace App\Repositories\Admin;

use App\Models\Main\Center;
use Illuminate\Support\Facades\DB;

class CenterRepository
{
    public function __construct(protected Center $center) {}

    /**
     * 建立中心
     */
    public function create(array $data)
    {
        return $this->center->create([
            'name' => $data['name'],
            'center_level_id' => $data['center_level_id'],
            'status' => $data['status'] ?? 1,
        ]);
    }

    /**
     * 依ID取得中心（找不到會拋例外）
     */
    public function findOrFail(int $id)
    {
        return $this->center->findOrFail($id);
    }

    /**
     * 依名稱查找中心，可排除指定ID
     */
    public function findByNameExcludeId(string $name, ?int $excludeId = null)
    {
        $query = $this->center->newQuery()->where('name', $name);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        return $query->first();
    }

    /**
     * 取得所有中心列表，支援篩選和分頁
     */
    public function getCenterWithFilterAndPaginate(array $filters = [], int $perPage = 15)
    {
        $query = $this->center->with(['level:id,name'])
            ->withCount(['activeStaff as active_staff_count']);

        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('centers.status', $filters['status']);
        }

        if (!empty($filters['center_level_id'])) {
            $query->where('centers.center_level_id', $filters['center_level_id']);
        }

        if (!empty($filters['name'])) {
            $query->where('centers.name', 'like', '%' . $filters['name'] . '%');
        }

        return $query->orderBy('centers.created_at', 'desc')->paginate($perPage);
    }
}
