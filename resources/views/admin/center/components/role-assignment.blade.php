{{-- 角色指派組件 --}}
<div class="role-assignment-container">
    @foreach($data['center_roles'] as $role)
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    {{ $role->name }}
                    @if($role->is_singleton)
                        <span class="badge badge-info">單一角色</span>
                    @else
                        <span class="badge badge-success">多人角色</span>
                    @endif
                </h6>
            </div>
            <div class="card-body">
                @if($role->is_singleton)
                    {{-- 單一角色指派介面 --}}
                    @include('admin.center.components.single-role-assignment', ['role' => $role])
                @else
                    {{-- 多人角色指派介面 --}}
                    @include('admin.center.components.multiple-role-assignment', ['role' => $role])
                @endif
            </div>
        </div>
    @endforeach
</div>

{{-- 角色指派相關的 CSS --}}
<style>
/* 載入動畫 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 自動完成下拉選單樣式 */
.account-suggestions-dropdown {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-suggestions-dropdown .ui-menu-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
}

.account-suggestions-dropdown .ui-menu-item:hover {
    background-color: #f8f9fa;
}

.account-suggestions-dropdown .ui-menu-item.ui-state-disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.account-suggestions-dropdown .ui-menu-item.ui-state-focus {
    background-color: #007bff;
    color: white;
}

/* 輸入框載入狀態 */
.input-loading-indicator {
    pointer-events: none;
}

/* 區塊載入遮罩 */
.block-loading-overlay {
    backdrop-filter: blur(1px);
}

/* 自定義通知樣式 */
.custom-notification {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: none;
    border-left: 4px solid;
}

.custom-notification.alert-success {
    border-left-color: #28a745;
}

.custom-notification.alert-danger {
    border-left-color: #dc3545;
}

.custom-notification.alert-warning {
    border-left-color: #ffc107;
}

.custom-notification.alert-info {
    border-left-color: #17a2b8;
}

/* 角色卡片增強樣式 */
.role-assignment-container .card {
    transition: box-shadow 0.2s ease;
}

.role-assignment-container .card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 成員項目樣式 */
.member-item {
    padding: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.member-item:hover {
    background-color: #e9ecef;
}

.selected-member {
    padding: 8px;
    border: 1px solid #28a745;
    border-radius: 4px;
    background-color: #d4edda;
}

/* 按鈕載入狀態 */
.btn[disabled] {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 響應式調整 */
@media (max-width: 768px) {
    .custom-notification {
        left: 10px;
        right: 10px;
        min-width: auto;
        max-width: none;
    }

    .member-item .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>

{{-- 角色指派相關的 JavaScript --}}
<script>
// 顯示通知函數
function showNotification(message, type = 'info') {
    // 移除現有的通知
    $('.notification-toast').remove();

    // 創建通知元素
    const typeClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const notification = $(`
        <div class="notification-toast alert ${typeClass} alert-dismissible" style="
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        ">
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
            ${message}
        </div>
    `);

    // 添加到頁面
    $('body').append(notification);

    // 3秒後自動消失
    setTimeout(() => {
        notification.fadeOut(300, () => notification.remove());
    }, 3000);
}

$(document).ready(function() {
    // 檢查 jQuery UI 是否載入
    if (typeof $.fn.autocomplete === 'undefined') {
        console.error('jQuery UI autocomplete 未載入');
        return;
    }

    // 檢查驗證物件是否存在
    if (typeof window.centerValidation === 'undefined') {
        console.warn('centerValidation 未初始化，稍後重試');
        setTimeout(function() {
            initializeAutocomplete();
            initializeRoleAssignment();
        }, 500);
    } else {
        // 初始化所有自動完成功能
        initializeAutocomplete();

        // 初始化角色指派事件
        initializeRoleAssignment();
    }
});

// 初始化自動完成功能
function initializeAutocomplete() {
    $('.account-autocomplete').each(function() {
        const $input = $(this);
        const roleCode = $input.data('role');

        // 暫時移除驗證屬性以排除驗證系統干擾
        // $input.attr('data-validate', 'accountSearch');
        $input.removeAttr('data-validate');

        // 清除任何現有的 autocomplete
        if ($input.hasClass('ui-autocomplete-input')) {
            $input.autocomplete('destroy');
        }

        $input.autocomplete({
            source: function(request, response) {

                // 驗證輸入長度
                if (request.term.length < 2) {
                    response([{
                        label: '請輸入至少2個字元',
                        value: '',
                        id: null,
                        disabled: true
                    }]);
                    return;
                }

                // 顯示載入狀態
                showInputLoading($input, true);

                $.ajax({
                    url: '/order/center/account-suggestions',
                    method: 'GET',
                    data: {
                        query: request.term,
                        limit: 10
                    },
                    success: function(apiResponse) {
                        // 隱藏載入狀態
                        showInputLoading($input, false);

                        if (apiResponse.status && apiResponse.data) {
                            // 使用 API 回應格式
                            const mappedData = $.map(apiResponse.data, function(item) {
                                return {
                                    label: item.label,
                                    value: item.value,
                                    id: item.id,
                                    name: item.name
                                };
                            });

                            response(mappedData);

                            // 如果沒有結果，顯示提示
                            if (apiResponse.data.length === 0) {
                                response([{
                                    label: '無匹配結果',
                                    value: '',
                                    id: null,
                                    disabled: true
                                }]);
                            }
                        } else {
                            console.error('API 回應格式錯誤:', apiResponse);
                            // 處理 API 錯誤
                            response([{
                                label: '搜尋失敗：' + (apiResponse.message || '未知錯誤'),
                                value: '',
                                id: null,
                                disabled: true
                            }]);
                        }
                    },
                    error: function(xhr) {
                        // 隱藏載入狀態
                        showInputLoading($input, false);

                        let errorMessage = '搜尋時發生錯誤';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.status === 401) {
                            errorMessage = '未登入或權限不足';
                        } else if (xhr.status === 404) {
                            errorMessage = 'API 端點不存在';
                        } else if (xhr.status === 500) {
                            errorMessage = '伺服器內部錯誤';
                        }

                        response([{
                            label: errorMessage,
                            value: '',
                            id: null,
                            disabled: true
                        }]);

                        // 顯示錯誤提示
                        showErrorMessage('會員搜尋失敗：' + errorMessage);
                    }
                });
            },
            minLength: 2,
            delay: 300,
            open: function() {
                const widget = $(this).autocomplete('widget');
                const $input = $(this);

                // 計算正確的位置
                const inputOffset = $input.offset();
                const inputHeight = $input.outerHeight();

                // 強制顯示和樣式，確保在正確位置
                widget.css({
                    'display': 'block !important',
                    'visibility': 'visible !important',
                    'z-index': '99999 !important',
                    'position': 'absolute !important',
                    'top': (inputOffset.top + inputHeight) + 'px !important',
                    'left': inputOffset.left + 'px !important',
                    'background': 'white !important',
                    'border': '2px solid #007bff !important',
                    'max-height': '200px !important',
                    'overflow-y': 'auto !important',
                    'box-shadow': '0 4px 12px rgba(0,0,0,0.3) !important',
                    'list-style': 'none !important',
                    'margin': '0 !important',
                    'padding': '0 !important',
                    'min-width': $input.outerWidth() + 'px !important'
                }).show();

                // 確保選單項目有正確樣式
                widget.find('li').css({
                    'padding': '8px 12px !important',
                    'cursor': 'pointer !important',
                    'border-bottom': '1px solid #eee !important',
                    'background': 'white !important'
                });

                // 設置懸停效果
                widget.find('li').hover(
                    function() { $(this).css('background-color', '#f8f9fa'); },
                    function() { $(this).css('background-color', 'white'); }
                );

            },
            close: function(event, ui) {
                // 只在用戶真正想關閉時才關閉（如選擇項目或按ESC）
                // 不要因為點擊其他地方而關閉
                const stack = new Error().stack;

                if (stack.includes('_closeOnClickOutside')) {
                    return false;
                }

                // 允許其他方式的關閉（如選擇項目、按ESC等）
                return true;
            },
            select: function(event, ui) {
                // 防止選擇禁用項目
                if (ui.item.disabled || !ui.item.id) {
                    return false;
                }

                // 儲存選中的會員資訊
                $input.data('account-id', ui.item.id);
                $input.data('account-name', ui.item.name || '');

                // 更新顯示
                updateAccountSelection($input, ui.item);

                return true;
            }
        });

        // 添加輸入框事件處理
        $input.on('input', function() {
            const value = $(this).val();
            if (value.length < 2) {
                // 清除之前的選擇
                $(this).removeData('account-id').removeData('account-name');
            }
        });
    });
}

// 更新會員選擇顯示
function updateAccountSelection($input, account) {
    const roleCode = $input.data('role');
    const isSingleton = $input.data('singleton');

    if (isSingleton) {
        // 單一角色：自動觸發指派
        const $container = $input.closest('.single-role-container');

        // 清空輸入框並隱藏
        $input.val('').hide();
        $container.find('.add-role-btn').hide();

        // 自動觸發角色指派
        assignRole(roleCode, account.id, true, $container);
    } else {
        // 多人角色：清空輸入框，準備添加
        $input.val('');
    }
}

// 初始化角色指派事件
function initializeRoleAssignment() {
    // 單一角色指派
    $(document).on('click', '.assign-single-role', function() {
        const $btn = $(this);
        const $container = $btn.closest('.single-role-container');
        const $input = $container.find('.account-autocomplete');
        const roleCode = $input.data('role');
        const accountId = $input.data('account-id');

        if (!accountId) {
            showNotification('請先選擇會員', 'warning');
            return;
        }

        assignRole(roleCode, accountId, true, $container);
    });

    // 多人角色指派
    $(document).on('click', '.assign-multiple-role', function() {
        const $btn = $(this);
        const $container = $btn.closest('.multiple-role-container');
        const $input = $container.find('.account-autocomplete');
        const roleCode = $input.data('role');
        const accountId = $input.data('account-id');

        if (!accountId) {
            showNotification('請先選擇會員', 'warning');
            return;
        }

        assignRole(roleCode, accountId, false, $container);
    });

    // 移除單一角色
    $(document).on('click', '.remove-single-role', function() {
        const $btn = $(this);
        const roleCode = $btn.data('role');
        const accountId = $btn.data('account-id');
        const $container = $btn.closest('.single-role-container');

        removeRole(roleCode, accountId, true, $container);
    });

    // 移除多人角色成員
    $(document).on('click', '.remove-multiple-role', function() {
        const $btn = $(this);
        const roleCode = $btn.data('role');
        const accountId = $btn.data('account-id');
        const $container = $btn.closest('.multiple-role-container');

        removeRole(roleCode, accountId, false, $container);
    });

    // 顯示單一角色輸入框
    $(document).on('click', '.show-single-input', function() {
        const $container = $(this).closest('.single-role-container');
        $container.find('.selected-account').hide();
        $container.find('.role-input-section').show();
        $container.find('.account-autocomplete').show().focus();
        $container.find('.add-role-btn').show();
        $(this).hide();
    });
}

// 根據會員ID獲取會員編號
function getAccountNumberById(accountId, $container) {
    // 先從容器中的輸入框獲取
    const $input = $container.find('.account-autocomplete');
    const accountNumber = $input.val();

    if (accountNumber && accountNumber.trim()) {
        return accountNumber.trim();
    }

    // 如果輸入框沒有值，嘗試從data屬性獲取
    const dataNumber = $input.data('account-number');
    if (dataNumber) {
        return dataNumber;
    }

    // 最後嘗試從全局緩存獲取（如果有的話）
    if (window.accountCache && window.accountCache[accountId]) {
        return window.accountCache[accountId].number;
    }

    return null;
}

// 指派角色
function assignRole(roleCode, accountId, isSingleton, $container) {
    const centerId = $('#center_id').val() || $('input[name="center_id"]').val();

    if (!centerId) {
        showNotification('中心ID不存在', 'error');
        return;
    }

    // 如果是單例角色，先進行驗證檢查
    if (isSingleton) {
        validateAndAssignSingletonRole(centerId, accountId, roleCode, $container);
    } else {
        performRoleAssignment(centerId, accountId, roleCode, isSingleton, $container);
    }
}

// 驗證並指派單例角色
function validateAndAssignSingletonRole(centerId, accountId, roleCode, $container) {
    // 先驗證角色指派
    $.ajax({
        url: '/order/center/validate-role-assignment',
        method: 'GET',
        data: {
            center_id: centerId,
            account_id: accountId,
            role_code: roleCode
        },
        success: function(response) {
            if (response.status && response.data) {
                const validation = response.data;

                if (!validation.valid) {
                    showNotification('無法指派角色：' + validation.conflicts.join(', '), 'error');
                    return;
                }

                // 檢查是否有警告（如單例角色衝突）
                if (validation.warnings && validation.warnings.length > 0) {
                    const singletonWarning = validation.warnings.find(w => w.type === 'singleton_conflict');
                    if (singletonWarning) {
                        const currentHolder = singletonWarning.current_holder;
                        const confirmMessage = `此角色目前由 ${currentHolder.number} ${currentHolder.name ? '(' + currentHolder.name + ')' : ''} 擔任。\n\n指派新角色後將自動結束其任期，確定要繼續嗎？`;

                        if (confirm(confirmMessage)) {
                            performRoleAssignment(centerId, accountId, roleCode, true, $container);
                        }
                        return;
                    }
                }

                // 沒有衝突，直接指派
                performRoleAssignment(centerId, accountId, roleCode, true, $container);
            } else {
                showNotification('驗證角色指派時發生錯誤', 'error');
            }
        },
        error: function(xhr) {
            console.error('驗證失敗:', xhr);
            // 驗證失敗時仍然嘗試指派，讓後端處理
            performRoleAssignment(centerId, accountId, roleCode, true, $container);
        }
    });
}

// 執行角色指派
function performRoleAssignment(centerId, accountId, roleCode, isSingleton, $container) {
    // 顯示載入狀態
    const $btn = $container.find(isSingleton ? '.assign-single-role' : '.assign-multiple-role');
    showButtonLoading($btn, true, '指派中...');

    // 顯示容器載入狀態
    showBlockLoading($container, true, '正在指派角色...');

    // 獲取會員編號
    const accountNumber = getAccountNumberById(accountId, $container);
    if (!accountNumber) {
        showErrorMessage('無法獲取會員編號');
        showButtonLoading($btn, false);
        showBlockLoading($container, false);
        return;
    }

    // 使用重試機制的 AJAX 請求
    retryAjaxRequest({
        url: '/order/center/assign-role',
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            center_id: centerId,
            account_number: accountNumber,
            role_code: roleCode
        },
        timeout: 10000 // 10秒超時
    }).done(function(response) {
        if (response.status) {
            // 成功後更新介面
            if (isSingleton) {
                updateSingleRoleDisplay($container, response.data);
            } else {
                addMultipleRoleMember($container, response.data);
            }

            // 清空輸入框
            const $input = $container.find('.account-autocomplete');
            $input.val('').removeData('account-id').removeData('account-name');

            // 顯示成功訊息，如果有替換現有角色則特別提示
            let message = '角色指派成功';
            if (response.data && response.data.replaced_existing) {
                message += '（已自動結束前任）';
                showInfoMessage('前任角色已自動結束，新角色已生效');
            }
            showSuccessMessage(message);

            // 刷新角色狀態顯示
            refreshRoleStatus(centerId, roleCode);

            // 更新現役人員概覽
            updateActiveStaffOverview();
        } else {
            showErrorMessage('指派失敗：' + response.message);
        }
    }).fail(function(xhr) {
        // 錯誤處理已在 handleAjaxError 中統一處理
        handleAjaxError(xhr, '角色指派失敗');
    }).always(function() {
        // 恢復按鈕和容器狀態
        showButtonLoading($btn, false);
        showBlockLoading($container, false);
    });
}

// 移除角色
function removeRole(roleCode, accountId, isSingleton, $container) {
    const centerId = $('#center_id').val() || $('input[name="center_id"]').val();

    if (!centerId) {
        showErrorMessage('中心ID不存在');
        return;
    }

    // 獲取會員資訊
    const $btn = $container.find(`[data-account-id="${accountId}"]`).filter('.remove-single-role, .remove-multiple-role');
    const $memberItem = $btn.closest('.selected-member, .member-item');
    const memberInfo = $memberItem.find('strong').text() || '未知會員';

    // 顯示載入狀態
    showButtonLoading($btn, true, '移除中...');
    showBlockLoading($container, true, '正在移除角色...');

    // 使用重試機制的 AJAX 請求
    retryAjaxRequest({
        url: '/order/center/remove-role',
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            center_id: centerId,
            account_id: accountId,
            role_code: roleCode
        },
        timeout: 10000 // 10秒超時
    }).done(function(response) {
        if (response.status) {
            // 成功後更新介面
            if (isSingleton) {
                resetSingleRoleDisplay($container);
                showInfoMessage('單一角色已移除，現在可以指派新的角色');
            } else {
                removeMultipleRoleMember($container, accountId);

                // 更新剩餘成員數量顯示
                if (response.data && response.data.remaining_count !== undefined) {
                    updateMembersCountFromServer($container, response.data.remaining_count);

                    if (response.data.remaining_count === 0) {
                        showInfoMessage('所有成員已移除，現在可以指派新的成員');
                    }
                }
            }

            showSuccessMessage(`會員「${memberInfo}」的角色移除成功`);

            // 刷新角色狀態顯示
            refreshRoleStatus(centerId, roleCode);

            // 更新現役人員概覽
            updateActiveStaffOverview();
        } else {
            showErrorMessage('移除失敗：' + response.message);
        }
    }).fail(function(xhr) {
        // 錯誤處理已在 handleAjaxError 中統一處理
        handleAjaxError(xhr, '角色移除失敗');
    }).always(function() {
        // 恢復按鈕和容器狀態
        showButtonLoading($btn, false);
        showBlockLoading($container, false);
    });
}

// 更新單一角色顯示
function updateSingleRoleDisplay($container, data) {
    const $selectedDiv = $container.find('.selected-account');
    const $input = $container.find('.account-autocomplete');
    const roleCode = $input.data('role');

    // 從輸入框獲取會員資訊
    const accountNumber = $input.val();
    const accountName = $input.data('account-name') || '';

    $selectedDiv.html(`
        <div class="selected-member d-flex justify-content-between align-items-center">
            <span>
                <strong>${accountNumber}</strong>
                ${accountName ? ' - ' + accountName : ''}
            </span>
            <button type="button" class="btn btn-sm btn-outline-danger remove-single-role"
                    data-role="${roleCode}" data-account-id="${$input.data('account-id')}">
                <i class="bi bi-x"></i> 移除
            </button>
        </div>
    `).show();

    $input.hide();
    $container.find('.add-role-btn').hide();
}

// 重置單一角色顯示
function resetSingleRoleDisplay($container) {
    $container.find('.selected-account').hide().empty();
    $container.find('.role-input-section').show();
    $container.find('.account-autocomplete').show();
    $container.find('.add-role-btn').show();
    $container.find('.show-single-input').hide();
}

// 添加多人角色成員
function addMultipleRoleMember($container, data) {
    const $membersList = $container.find('.members-list');
    const $input = $container.find('.account-autocomplete');
    const roleCode = $input.data('role');

    // 從輸入框獲取會員資訊
    const accountNumber = $input.val();
    const accountName = $input.data('account-name') || '';

    const memberHtml = `
        <div class="member-item d-flex justify-content-between align-items-center mb-2" data-account-id="${$input.data('account-id')}">
            <span>
                <strong>${accountNumber}</strong>
                ${accountName ? ' - ' + accountName : ''}
            </span>
            <button type="button" class="btn btn-sm btn-outline-danger remove-multiple-role"
                    data-role="${roleCode}" data-account-id="${$input.data('account-id')}">
                <i class="bi bi-x"></i> 移除
            </button>
        </div>
    `;

    $membersList.append(memberHtml);

    // 更新成員數量顯示
    updateMembersCount($container);
}

// 移除多人角色成員
function removeMultipleRoleMember($container, accountId) {
    $container.find(`.member-item[data-account-id="${accountId}"]`).remove();

    // 更新成員數量顯示
    updateMembersCount($container);
}

// 更新成員數量顯示
function updateMembersCount($container) {
    const count = $container.find('.member-item').length;
    const $countBadge = $container.find('.members-count');

    if (count > 0) {
        $countBadge.text(`(${count}人)`).show();
    } else {
        $countBadge.hide();
    }
}

// 從伺服器回應更新成員數量顯示
function updateMembersCountFromServer($container, count) {
    const $countBadge = $container.find('.members-count');
    const $noMembers = $container.find('.no-members');

    if (count > 0) {
        $countBadge.text(`(${count}人)`).show();
        $noMembers.hide();
    } else {
        $countBadge.hide();
        $noMembers.show();
    }
}

// 顯示成功訊息
function showSuccessMessage(message) {
    // 可以根據現有系統的通知方式來實現
    // 這裡使用簡單的 alert，實際可以替換為更好的通知組件
    if (typeof toastr !== 'undefined') {
        toastr.success(message);
    } else {
        // 創建自定義成功提示
        showCustomNotification(message, 'success');
    }
}

// 顯示錯誤訊息
function showErrorMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        // 創建自定義錯誤提示
        showCustomNotification('錯誤：' + message, 'error');
    }
}

// 顯示警告訊息
function showWarningMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.warning(message);
    } else {
        // 創建自定義警告提示
        showCustomNotification('警告：' + message, 'warning');
    }
}

// 顯示資訊訊息
function showInfoMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.info(message);
    } else {
        showCustomNotification(message, 'info');
    }
}

// 自定義通知組件
function showCustomNotification(message, type = 'info') {
    // 移除現有通知
    $('.custom-notification').remove();

    const typeClasses = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    const typeIcons = {
        'success': 'bi-check-circle',
        'error': 'bi-exclamation-triangle',
        'warning': 'bi-exclamation-circle',
        'info': 'bi-info-circle'
    };

    const notification = $(`
        <div class="custom-notification alert ${typeClasses[type]} alert-dismissible fade show"
             style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
            <i class="bi ${typeIcons[type]} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    // 自動隱藏
    setTimeout(function() {
        notification.fadeOut(300, function() {
            $(this).remove();
        });
    }, type === 'error' ? 8000 : 5000);
}

// 顯示輸入框載入狀態
function showInputLoading($input, show) {
    const $container = $input.closest('.form-group, .input-group');

    if (show) {
        // 添加載入指示器
        if ($container.find('.input-loading-indicator').length === 0) {
            const loadingHtml = `
                <div class="input-loading-indicator" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); z-index: 10;">
                    <i class="bi bi-hourglass-split text-muted" style="animation: spin 1s linear infinite;"></i>
                </div>
            `;
            $container.css('position', 'relative').append(loadingHtml);
        }

        // 不要禁用輸入框，只顯示載入動畫
        // $input.prop('disabled', true);
    } else {
        // 移除載入指示器
        $container.find('.input-loading-indicator').remove();

        // 不需要啟用輸入框，因為沒有禁用
        // $input.prop('disabled', false);
    }
}

// 顯示按鈕載入狀態
function showButtonLoading($button, show, loadingText = '處理中...') {
    if (show) {
        // 儲存原始文字和狀態
        if (!$button.data('original-text')) {
            $button.data('original-text', $button.html());
            $button.data('original-disabled', $button.prop('disabled'));
        }

        // 設置載入狀態
        $button.prop('disabled', true).html(`
            <i class="bi bi-hourglass-split me-1" style="animation: spin 1s linear infinite;"></i>
            ${loadingText}
        `);
    } else {
        // 恢復原始狀態
        const originalText = $button.data('original-text');
        const originalDisabled = $button.data('original-disabled');

        if (originalText) {
            $button.html(originalText);
            $button.prop('disabled', originalDisabled || false);

            // 清除儲存的資料
            $button.removeData('original-text').removeData('original-disabled');
        }
    }
}

// 顯示區塊載入狀態
function showBlockLoading($container, show, message = '載入中...') {
    if (show) {
        // 添加載入遮罩
        if ($container.find('.block-loading-overlay').length === 0) {
            const loadingHtml = `
                <div class="block-loading-overlay" style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 100;
                    border-radius: inherit;
                ">
                    <div class="text-center">
                        <i class="bi bi-hourglass-split text-primary mb-2" style="font-size: 2rem; animation: spin 1s linear infinite;"></i>
                        <div class="text-muted">${message}</div>
                    </div>
                </div>
            `;
            $container.css('position', 'relative').append(loadingHtml);
        }
    } else {
        // 移除載入遮罩
        $container.find('.block-loading-overlay').remove();
    }
}

// 處理 AJAX 錯誤的通用函數
function handleAjaxError(xhr, defaultMessage = '操作失敗') {
    let errorMessage = defaultMessage;

    if (xhr.responseJSON) {
        if (xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseJSON.errors) {
            // 處理驗證錯誤
            const errors = xhr.responseJSON.errors;
            const firstError = Object.values(errors)[0];
            if (Array.isArray(firstError)) {
                errorMessage = firstError[0];
            } else {
                errorMessage = firstError;
            }
        }
    } else if (xhr.status === 0) {
        errorMessage = '網路連線失敗，請檢查網路狀態';
    } else if (xhr.status === 404) {
        errorMessage = '請求的資源不存在';
    } else if (xhr.status === 403) {
        errorMessage = '沒有權限執行此操作';
    } else if (xhr.status === 422) {
        errorMessage = '提交的資料格式不正確';
    } else if (xhr.status === 500) {
        errorMessage = '伺服器內部錯誤，請稍後再試';
    } else if (xhr.status >= 400) {
        errorMessage = `請求失敗 (${xhr.status})`;
    }

    showErrorMessage(errorMessage);

    // 記錄錯誤到控制台
    console.error('AJAX Error:', {
        status: xhr.status,
        statusText: xhr.statusText,
        responseText: xhr.responseText,
        url: xhr.responseURL || 'unknown'
    });

    return errorMessage;
}

// 重試機制
function retryAjaxRequest(ajaxOptions, maxRetries = 3, retryDelay = 1000) {
    let retryCount = 0;

    function makeRequest() {
        return $.ajax(ajaxOptions).fail(function(xhr) {
            retryCount++;

            // 如果是網路錯誤且還有重試次數
            if (xhr.status === 0 && retryCount < maxRetries) {
                showWarningMessage(`網路連線失敗，${retryDelay / 1000} 秒後重試... (${retryCount}/${maxRetries})`);

                setTimeout(function() {
                    makeRequest();
                }, retryDelay);
            } else {
                // 重試次數用完或其他錯誤
                handleAjaxError(xhr);
            }
        });
    }

    return makeRequest();
}

// 刷新角色狀態顯示
function refreshRoleStatus(centerId, roleCode = null) {
    const requestData = {
        center_id: centerId
    };

    // 如果指定了角色，只刷新該角色
    if (roleCode) {
        requestData.role_codes = [roleCode];
    }

    $.ajax({
        url: '/order/center/role-status',
        method: 'GET',
        data: requestData,
        success: function(response) {
            if (response.status && response.data) {
                updateRoleStatusDisplay(response.data.roles);
            }
        },
        error: function(xhr) {
            console.warn('刷新角色狀態失敗:', xhr);
            // 不顯示錯誤訊息，因為這是背景更新
        }
    });
}

// 更新角色狀態顯示
function updateRoleStatusDisplay(rolesData) {
    $.each(rolesData, function(roleCode, roleInfo) {
        const $container = $(`.role-assignment-container [data-role="${roleCode}"]`).closest('.card');

        if ($container.length === 0) {
            return;
        }

        // 更新角色標題中的人數顯示
        const $header = $container.find('.card-header h6');
        const roleName = roleInfo.role_name;
        const activeCount = roleInfo.active_count;

        let headerText = roleName;
        if (roleInfo.is_singleton) {
            headerText += ` <span class="badge badge-info">單一角色</span>`;
            if (activeCount > 0) {
                headerText += ` <span class="badge badge-success">已指派</span>`;
            }
        } else {
            headerText += ` <span class="badge badge-success">多人角色</span>`;
            if (activeCount > 0) {
                headerText += ` <span class="badge badge-primary">${activeCount}人</span>`;
            }
        }

        $header.html(headerText);

        // 更新具體的角色成員顯示
        if (roleInfo.is_singleton) {
            updateSingletonRoleDisplay($container, roleInfo);
        } else {
            updateMultipleRoleDisplay($container, roleInfo);
        }
    });
}

// 更新單例角色顯示
function updateSingletonRoleDisplay($container, roleInfo) {
    const $selectedDiv = $container.find('.selected-account');
    const $input = $container.find('.account-autocomplete');
    const $addBtn = $container.find('.add-role-btn');
    const $showInputBtn = $container.find('.show-single-input');

    if (roleInfo.active_count > 0 && roleInfo.staff_list.length > 0) {
        const staff = roleInfo.staff_list[0];
        const roleCode = $input.data('role');

        $selectedDiv.html(`
            <div class="selected-member d-flex justify-content-between align-items-center">
                <span>
                    <strong>${staff.account_number}</strong>
                    ${staff.account_name ? ' - ' + staff.account_name : ''}
                    <small class="text-muted d-block">開始時間: ${formatDateTime(staff.start_at)}</small>
                </span>
                <button type="button" class="btn btn-sm btn-outline-danger remove-single-role"
                        data-role="${roleCode}" data-account-id="${staff.account_id}">
                    <i class="bi bi-x"></i> 移除
                </button>
            </div>
        `).show();

        $input.hide();
        $addBtn.hide();
        $showInputBtn.show();
    } else {
        $selectedDiv.hide().empty();
        $input.show();
        $addBtn.show();
        $showInputBtn.hide();
    }
}

// 更新多人角色顯示
function updateMultipleRoleDisplay($container, roleInfo) {
    const $membersList = $container.find('.members-list');
    const $noMembers = $container.find('.no-members');
    const $countBadge = $container.find('.members-count');
    const roleCode = $container.find('.account-autocomplete').data('role');

    // 清空現有列表
    $membersList.empty();

    if (roleInfo.active_count > 0 && roleInfo.staff_list.length > 0) {
        // 添加所有成員
        roleInfo.staff_list.forEach(function(staff) {
            const memberHtml = `
                <div class="member-item d-flex justify-content-between align-items-center mb-2" data-account-id="${staff.account_id}">
                    <span>
                        <strong>${staff.account_number}</strong>
                        ${staff.account_name ? ' - ' + staff.account_name : ''}
                        <small class="text-muted d-block">開始時間: ${formatDateTime(staff.start_at)}</small>
                    </span>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-multiple-role"
                            data-role="${roleCode}" data-account-id="${staff.account_id}">
                        <i class="bi bi-x"></i> 移除
                    </button>
                </div>
            `;
            $membersList.append(memberHtml);
        });

        $countBadge.text(`(${roleInfo.active_count}人)`).show();
        $noMembers.hide();
    } else {
        $countBadge.hide();
        $noMembers.show();
    }
}

// 格式化日期時間
function formatDateTime(dateTimeString) {
    if (!dateTimeString) {
        return '未設定';
    }

    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateTimeString;
    }
}

// 自動刷新角色狀態（可選功能）
function startAutoRefresh(centerId, intervalSeconds = 30) {
    if (window.roleStatusRefreshInterval) {
        clearInterval(window.roleStatusRefreshInterval);
    }

    window.roleStatusRefreshInterval = setInterval(function() {
        refreshRoleStatus(centerId);
    }, intervalSeconds * 1000);
}

// 停止自動刷新
function stopAutoRefresh() {
    if (window.roleStatusRefreshInterval) {
        clearInterval(window.roleStatusRefreshInterval);
        window.roleStatusRefreshInterval = null;
    }
}

// 頁面離開時清理
$(window).on('beforeunload', function() {
    stopAutoRefresh();
});

// 批量指派角色（用於多人角色的批量操作）
function batchAssignRole(roleCode, accountIds, $container) {
    const centerId = $('#center_id').val() || $('input[name="center_id"]').val();

    if (!centerId) {
        showErrorMessage('中心ID不存在');
        return;
    }

    if (!accountIds || accountIds.length === 0) {
        showErrorMessage('請選擇要指派的會員');
        return;
    }

    // 顯示載入狀態
    const $btn = $container.find('.batch-assign-btn');
    if ($btn.length > 0) {
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 批量指派中...');
    }

    $.ajax({
        url: '/order/center/batch-assign-role',
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            center_id: centerId,
            account_ids: accountIds,
            role_code: roleCode
        },
        success: function(response) {
            if (response.status) {
                const data = response.data;

                // 顯示結果訊息
                let message = `批量指派完成：成功 ${data.successful_count} 人`;
                if (data.failed_count > 0) {
                    message += `，失敗 ${data.failed_count} 人`;
                }

                if (data.successful_count > 0) {
                    showSuccessMessage(message);
                    // 重新載入頁面或更新介面
                    location.reload();
                } else {
                    showWarningMessage(message);
                }

                // 顯示失敗詳情
                if (data.failed && data.failed.length > 0) {
                    console.log('批量指派失敗詳情:', data.failed);
                }
            } else {
                showErrorMessage('批量指派失敗：' + response.message);
            }
        },
        error: function(xhr) {
            let message = '批量指派時發生錯誤';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showErrorMessage(message);
        },
        complete: function() {
            // 恢復按鈕狀態
            if ($btn.length > 0) {
                $btn.prop('disabled', false).html(originalText);
            }
        }
    });
}

// 批量移除角色
function batchRemoveRole(roleCode, accountIds, $container) {
    const centerId = $('#center_id').val() || $('input[name="center_id"]').val();

    if (!centerId) {
        showErrorMessage('中心ID不存在');
        return;
    }

    if (!accountIds || accountIds.length === 0) {
        showErrorMessage('請選擇要移除的會員');
        return;
    }



    $.ajax({
        url: '/order/center/batch-remove-role',
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            center_id: centerId,
            account_ids: accountIds,
            role_code: roleCode
        },
        success: function(response) {
            if (response.status) {
                const data = response.data;

                // 顯示結果訊息
                let message = `批量移除完成：成功 ${data.successful_count} 人`;
                if (data.failed_count > 0) {
                    message += `，失敗 ${data.failed_count} 人`;
                }

                if (data.successful_count > 0) {
                    showSuccessMessage(message);
                    // 重新載入頁面或更新介面
                    location.reload();
                } else {
                    showWarningMessage(message);
                }

                // 顯示失敗詳情
                if (data.failed && data.failed.length > 0) {
                    console.log('批量移除失敗詳情:', data.failed);
                }
            } else {
                showErrorMessage('批量移除失敗：' + response.message);
            }
        },
        error: function(xhr) {
            let message = '批量移除時發生錯誤';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showErrorMessage(message);
        }
    });
}

// 更新現役人員概覽
function updateActiveStaffOverview() {
    const centerId = $('#center_id').val() || $('input[name="center_id"]').val();

    if (!centerId) {
        console.warn('無法獲取中心ID，跳過現役人員概覽更新');
        return;
    }

    // 發送AJAX請求獲取最新的現役人員資料
    $.ajax({
        url: '/order/center/active-staff-overview',
        method: 'GET',
        data: {
            center_id: centerId
        },
        timeout: 5000
    }).done(function(response) {
        if (response.status && response.data) {
            // 更新現役人員概覽區域
            updateOverviewDisplay(response.data);
        }
    }).fail(function(xhr) {
        console.warn('現役人員概覽更新失敗:', xhr.responseText);
        // 不顯示錯誤訊息，避免干擾用戶操作
    });
}

// 更新概覽顯示
function updateOverviewDisplay(data) {
    // 更新人數
    const $overviewBadge = $('h5:contains("現役人員概覽")').find('.badge');
    if ($overviewBadge.length > 0) {
        const totalCount = data.total_count || 0;
        $overviewBadge.text(`${totalCount}人`);
    }

    // 重新生成現役人員概覽的HTML
    const $overviewCard = $('h5:contains("現役人員概覽")').closest('.card');
    const $overviewBody = $overviewCard.find('.card-body .row');

    if ($overviewBody.length > 0) {
        // 清空現有內容
        $overviewBody.empty();

        // 角色順序和映射
        const roleOrder = ['founder', 'director', 'executive_director', 'lecturer', 'market', 'sales'];
        const roleMapping = {
            'founder': '中心發起人',
            'director': '中心總監',
            'executive_director': '大總監',
            'lecturer': '講師',
            'market': '行政/廣告',
            'sales': '業務'
        };

        // 按順序生成每個角色的HTML
        roleOrder.forEach(roleCode => {
            const roleName = roleMapping[roleCode];
            const roleData = data.roles[roleCode];

            const $roleColumn = $('<div class="col-md-4 mb-3"></div>');
            const $staffSummary = $('<div class="staff-summary"></div>');

            // 添加角色標題
            const $roleTitle = $(`<h6 class="text-muted">${roleName}</h6>`);
            $staffSummary.append($roleTitle);

            if (roleData && roleData.length > 0) {
                // 有成員時顯示成員列表
                const $staffList = $('<div class="staff-list"></div>');

                roleData.forEach(member => {
                    const $staffItem = $(`
                        <div class="staff-item">
                            <span class="badge badge-success">
                                ${member.account_number} - ${member.account_name}
                            </span>
                        </div>
                    `);
                    $staffList.append($staffItem);
                });

                $staffSummary.append($staffList);
            } else {
                // 沒有成員時顯示尚未指派
                const $emptyText = $(`
                    <span class="text-muted">
                        <small>尚未指派</small>
                    </span>
                `);
                $staffSummary.append($emptyText);
            }

            $roleColumn.append($staffSummary);
            $overviewBody.append($roleColumn);
        });
    }
}
</script>
